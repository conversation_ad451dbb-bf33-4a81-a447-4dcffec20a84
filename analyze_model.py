#!/usr/bin/env python3
"""
分析PyTorch模型结构的脚本
"""
import torch
import torch.nn as nn
import sys
import os
import yaml
from collections import OrderedDict

# 添加项目路径
sys.path.append('./work_dir/baseline_res18_attention')
sys.path.append('.')

# 导入模型相关模块
from work_dir.baseline_res18_attention.slr_network import SLRModel
from modules.attention import DynamicTemporalAttention

def load_model_config(config_path):
    """加载模型配置"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def analyze_model_structure(model_path, config_path):
    """分析模型结构"""
    print("=" * 80)
    print("模型结构分析")
    print("=" * 80)
    
    # 加载配置
    config = load_model_config(config_path)
    print(f"配置文件: {config_path}")
    print(f"模型文件: {model_path}")
    print()
    
    # 提取模型参数
    model_args = config['model_args']
    attention_params = config.get('attention_params', None)
    
    print("模型配置参数:")
    for key, value in model_args.items():
        print(f"  {key}: {value}")
    
    if attention_params:
        print("\n注意力机制参数:")
        for key, value in attention_params.items():
            print(f"  {key}: {value}")
    print()
    
    # 创建模型实例
    try:
        model = SLRModel(
            num_classes=model_args['num_classes'],
            c2d_type=model_args['c2d_type'],
            conv_type=model_args['conv_type'],
            use_bn=model_args.get('use_bn', False),
            hidden_size=1024,  # 默认值
            weight_norm=model_args.get('weight_norm', True),
            share_classifier=model_args.get('share_classifier', True),
            attention_params=attention_params
        )
        print("✓ 模型实例创建成功")
    except Exception as e:
        print(f"✗ 模型实例创建失败: {e}")
        return
    
    # 加载模型权重
    try:
        if os.path.exists(model_path):
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # 处理可能的DataParallel包装
            if 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint
                
            # 移除可能的module.前缀
            new_state_dict = OrderedDict()
            for k, v in state_dict.items():
                name = k[7:] if k.startswith('module.') else k
                new_state_dict[name] = v
            
            model.load_state_dict(new_state_dict, strict=False)
            print("✓ 模型权重加载成功")
        else:
            print(f"✗ 模型文件不存在: {model_path}")
            return
    except Exception as e:
        print(f"✗ 模型权重加载失败: {e}")
        return
    
    print()
    print("=" * 80)
    print("模型结构详细信息")
    print("=" * 80)
    
    # 分析模型结构
    print("1. 整体架构:")
    print("   SLRModel (手语识别模型)")
    print("   ├── conv2d: ResNet18 + 动态时间注意力机制")
    print("   ├── conv1d: 时间卷积层")
    print("   ├── temporal_model: 双向LSTM")
    print("   └── classifier: 分类器")
    print()
    
    # 分析各个组件
    print("2. 主要组件详情:")
    
    # ResNet backbone
    print("\n   2.1 ResNet18 Backbone:")
    resnet = model.conv2d
    print(f"       - 输入: 3通道RGB视频帧")
    print(f"       - 输出: 512维特征")
    print(f"       - 层数: ResNet18 (2+2+2+2层)")
    
    # 注意力机制
    print("\n   2.2 动态时间注意力机制:")
    attention_modules = []
    for name, module in model.named_modules():
        if isinstance(module, DynamicTemporalAttention):
            attention_modules.append((name, module))
    
    for name, module in attention_modules:
        print(f"       - {name}:")
        print(f"         * 通道数: {module.channels}")
        print(f"         * 最大窗口大小: {module.max_window_size}")
        print(f"         * 卷积核大小: {module.kernel_sizes}")
        print(f"         * 通道缩减比例: {module.reduction_ratio}")
    
    # 时间卷积
    print("\n   2.3 时间卷积层:")
    print(f"       - 输入维度: 512")
    print(f"       - 隐藏维度: 1024")
    print(f"       - 卷积类型: {model_args['conv_type']}")
    
    # LSTM
    print("\n   2.4 双向LSTM:")
    print(f"       - 输入维度: 1024")
    print(f"       - 隐藏维度: 1024")
    print(f"       - 层数: 2")
    print(f"       - 双向: True")
    
    # 分类器
    print("\n   2.5 分类器:")
    print(f"       - 输入维度: 1024")
    print(f"       - 输出维度: {model_args['num_classes']} (词汇表大小)")
    print(f"       - 权重归一化: {model_args.get('weight_norm', True)}")
    
    print()
    print("=" * 80)
    print("参数统计")
    print("=" * 80)
    
    # 统计参数
    total_params = 0
    trainable_params = 0
    attention_params = 0
    
    for name, param in model.named_parameters():
        total_params += param.numel()
        if param.requires_grad:
            trainable_params += param.numel()
        
        # 统计注意力模块参数
        if any(attn_name in name for attn_name, _ in attention_modules):
            attention_params += param.numel()
    
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    print(f"注意力模块参数量: {attention_params:,} ({attention_params/total_params*100:.2f}%)")
    
    # 按模块统计参数
    print("\n各模块参数量:")
    module_params = {}
    for name, module in model.named_children():
        params = sum(p.numel() for p in module.parameters())
        module_params[name] = params
        print(f"  {name}: {params:,}")
    
    print()
    print("=" * 80)
    print("模型输入输出")
    print("=" * 80)
    
    print("输入:")
    print("  - 视频序列: (batch_size, time_steps, channels=3, height=224, width=224)")
    print("  - 序列长度: (batch_size,)")
    print()
    print("输出:")
    print("  - conv_logits: 卷积层的分类结果")
    print("  - sequence_logits: LSTM层的分类结果")
    print("  - recognized_sents: 识别的句子")
    print("  - feat_len: 特征序列长度")
    
    print()
    print("=" * 80)
    print("分析完成")
    print("=" * 80)

if __name__ == "__main__":
    model_path = "./work_dir/baseline_res18_attention/_best_model.pt"
    config_path = "./work_dir/baseline_res18_attention/config.yaml"
    
    analyze_model_structure(model_path, config_path)
